FROM condaforge/mambaforge:latest

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    wget \
    git \
    vim \
    && rm -rf /var/lib/apt/lists/*

# 创建并激活 conda 环境
RUN conda create -n trading python=3.11 -y
RUN echo "conda activate trading" >> ~/.bashrc

# 使用 conda 安装关键依赖
RUN conda install -n trading -c conda-forge ta-lib numpy pandas -y && \
    conda install -n trading -c plotly plotly -y && \
    conda install -n trading -c conda-forge dash -y && \
    conda install -n trading -c conda-forge backtrader -y || pip install --no-cache-dir backtrader

# 设置正确的环境变量
ENV PATH /opt/conda/envs/trading/bin:$PATH

# 设置 shell 为 bash，以便正确激活 conda 环境
SHELL ["/bin/bash", "-c"]

# 将在容器运行后通过 postCreateCommand 安装剩余依赖