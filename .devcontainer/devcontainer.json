{"name": "Quantitative Trading System", "build": {"dockerfile": "Dockerfile", "context": ".."}, "customizations": {"vscode": {"extensions": ["ms-python.python", "ms-python.vscode-pylance", "ms-toolsai.jupyter"], "settings": {"python.defaultInterpreterPath": "/opt/conda/envs/trading/bin/python", "python.linting.enabled": true, "python.formatting.provider": "autopep8", "editor.formatOnSave": true}}}, "forwardPorts": [8050], "postCreateCommand": "source activate trading && pip install -r requirements.txt || echo 'Some packages may have failed to install'", "remoteUser": "root"}