# 检查容器是否存在
if [ "$(docker ps -aq -f name=quant_dev_weilint)" ]; then
    # 检查容器是否在运行
    if [ "$(docker ps -q -f name=quant_dev_weilint)" ]; then
        # 如果容器在运行，直接进入
        docker exec -it quant_dev_weilint /bin/bash
    else
        # 如果容器存在但未运行，启动并进入
        docker start quant_dev_weilint
        docker exec -it quant_dev_weilint /bin/bash
    fi
else
    # 如果容器不存在，创建新容器
    docker run -it --name quant_dev_weilint quant_dev:v1.0 /bin/bash
fi
